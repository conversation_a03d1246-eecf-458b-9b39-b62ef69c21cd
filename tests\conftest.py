"""Shared pytest fixtures for all test modules.

This file contains reusable fixtures that can be used across multiple test files.
Pytest automatically discovers and makes available all fixtures defined in conftest.py.
"""

import pytest
import numpy as np
from unittest.mock import Mock, MagicMock
from meshio import CellBlock

from msgd.design.distribution import Distribution
from msgd.core._data_classes import EntitySet


# =============================================================================
# Mesh Data Fixtures
# =============================================================================

@pytest.fixture
def sample_mesh_nodes():
    """Fixture providing sample 2D mesh nodes."""
    return np.array([
        [0.0, 0.0],  # node 0
        [1.0, 0.0],  # node 1
        [2.0, 0.0],  # node 2
        [0.0, 1.0],  # node 3
        [1.0, 1.0],  # node 4
        [2.0, 1.0],  # node 5
    ])


@pytest.fixture
def sample_mesh_nodes_2d_3x3():
    """Fixture providing sample 2D mesh nodes for a 3x3 grid.

    Nodes are indexed as follows:
    6 7 8
    3 4 5
    0 1 2
    """
    return np.array([
        [0.0, 0.0],  # node 0
        [1.0, 0.0],  # node 1
        [2.0, 0.0],  # node 2
        [0.0, 1.0],  # node 3
        [1.0, 1.0],  # node 4
        [2.0, 1.0],  # node 5
        [0.0, 2.0],  # node 6
        [1.0, 2.0],  # node 7
        [2.0, 2.0],  # node 8
    ])


@pytest.fixture
def sample_mesh_nodes_3d():
    """Fixture providing sample 3D mesh nodes."""
    return np.array([
        [0.0, 0.0, 0.0],  # node 0
        [1.0, 0.0, 0.0],  # node 1
        [2.0, 0.0, 0.0],  # node 2
        [0.0, 1.0, 0.0],  # node 3
        [1.0, 1.0, 0.0],  # node 4
        [2.0, 1.0, 0.0],  # node 5
        [0.0, 0.0, 1.0],  # node 6
        [1.0, 0.0, 1.0],  # node 7
        [2.0, 0.0, 1.0],  # node 8
    ])


@pytest.fixture
def sample_mesh_cells_with_etags_2d_quad_2x2():
    """Fixture providing sample 2D mesh cells for a 2x2 quad grid."""
    mesh_cells = [
        CellBlock('quad', np.array([
            [0, 1, 4, 3],  # element 1
            [1, 2, 5, 4],  # element 2
            [3, 4, 7, 6],  # element 3
            [4, 5, 8, 7],  # element 4
        ]))
    ]

    cell_data_etags = [
        np.array([1, 2, 3, 4])
    ]

    return mesh_cells, cell_data_etags


@pytest.fixture
def sample_mesh_cells():
    """Fixture providing sample mesh cells (triangles and quads)."""
    return [
        CellBlock('triangle', np.array([
            [0, 1, 3],  # element 1
            [1, 4, 3],  # element 2
            [1, 2, 4],  # element 3
        ])),
        CellBlock('quad', np.array([
            [2, 5, 4, 1],  # element 4
        ]))
    ]


@pytest.fixture
def sample_cell_data_etags():
    """Fixture providing element tags corresponding to mesh cells."""
    return [
        np.array([1, 2, 3]),  # triangle element IDs
        np.array([4])         # quad element IDs
    ]


@pytest.fixture
def simple_mesh_data():
    """Create simple mesh data for testing eval_distributions_on_region_nodes."""
    # Create simple triangular mesh
    mesh_cells = [
        CellBlock('triangle', np.array([
            [0, 1, 2],  # Element 1
            [1, 2, 3],  # Element 2
            [2, 3, 4]   # Element 3
        ]))
    ]

    cell_data_etags = [
        np.array([1, 2, 3])  # Element IDs
    ]

    # Node coordinates (5 nodes)
    nodes = np.array([
        [0.0, 0.0],  # Node 0
        [1.0, 0.0],  # Node 1
        [0.5, 1.0],  # Node 2
        [1.5, 1.0],  # Node 3
        [1.0, 2.0]   # Node 4
    ])

    # Regions mapping using EntitySet objects
    regions = {
        'region1': EntitySet(name='region1', type='element', items=[1, 2]),    # Elements 1 and 2
        'region2': EntitySet(name='region2', type='element', items=[3]),       # Element 3
        'all': EntitySet(name='all', type='element', items=[1, 2, 3])          # All elements
    }

    return mesh_cells, cell_data_etags, nodes, regions


@pytest.fixture
def simple_mesh_2d_quad():
    """Create a simple 2D quad mesh for testing.

    Points and cells are indexed as follows:
    6---7---8
    |   |   |
    | 3 | 4 |
    |   |   |
    3---4---5
    |   |   |
    | 1 | 2 |
    |   |   |
    0---1---2
    """

    points = np.array([
        [0.0, 0.0],  # node 0
        [1.0, 0.0],  # node 1
        [2.0, 0.0],  # node 2
        [0.0, 1.0],  # node 3
        [1.0, 1.0],  # node 4
        [2.0, 1.0],  # node 5
        [0.0, 2.0],  # node 6
        [1.0, 2.0],  # node 7
        [2.0, 2.0],  # node 8
    ])

    cells = [
        CellBlock('quad', np.array([
            [0, 1, 4, 3],  # element 1
            [1, 2, 5, 4],  # element 2
            [3, 4, 7, 6],  # element 3
            [4, 5, 8, 7],  # element 4
        ]))
    ]

    cell_data_etags = [
        np.array([1, 2, 3, 4])
    ]

    regions = {
        'region1': EntitySet(name='region1', type='element', items=[1, 2, 3, 4]),    # Elements 1 and 2
        'region2': EntitySet(name='region2', type='element', items=[3, 4]),   # Elements 3 and 4
    }

    return points, cells, cell_data_etags, regions









# =============================================================================
# Function Fixtures (for Distribution testing)
# =============================================================================

@pytest.fixture
def linear_function():
    """Create a simple linear function for testing.
    
    y = 2x + 1
    """
    import msgd.utils.function as mutils

    class LinearFunction(mutils.MSGDFunction):
        def __init__(self):
            super().__init__()
            self.name = "linear"
            self.vtype = "float"

        def __call__(self, x):
            # Handle both 1D and 2D input
            x = np.asarray(x)
            if x.ndim == 1:
                return 2 * x + 1
            else:
                return 2 * x[:, 0] + 1  # Use first coordinate

    return LinearFunction()


@pytest.fixture
def quadratic_function():
    """Create a quadratic function for testing.
    
    y = x^2
    """
    import msgd.utils.function as mutils

    class QuadraticFunction(mutils.MSGDFunction):
        def __init__(self):
            super().__init__()
            self.name = "quadratic"
            self.vtype = "float"

        def __call__(self, x):
            # Handle both 1D and 2D input
            x = np.asarray(x)
            if x.ndim == 1:
                return x**2
            else:
                return x[:, 0]**2  # Use first coordinate

    return QuadraticFunction()









# =============================================================================
# Distribution Fixtures
# =============================================================================

@pytest.fixture
def mock_distribution():
    """Fixture providing a mock Distribution object."""
    distribution = Mock(spec=Distribution)
    distribution.name = 'test_param'
    distribution.region = 'test_region'
    distribution.return_value = np.array([[1.0], [2.0], [3.0]])
    return distribution


@pytest.fixture
def single_param_distribution(linear_function):
    """Create a distribution with single parameter."""
    dist = Distribution(
        name="thickness",
        region="region1",
        func_base=linear_function,
        vtype="float"
    )
    dist._function = [linear_function]
    return dist


@pytest.fixture
def multi_param_distribution(linear_function, quadratic_function):
    """Create a distribution with multiple parameters."""
    dist = Distribution(
        name=["thickness", "density"],
        region="region2",
        func_base=None,
        vtype="float"
    )
    dist._function = [linear_function, quadratic_function]
    return dist


@pytest.fixture
def sample_distributions():
    """Fixture providing a list of sample distributions."""
    # Create mock distributions
    dist1 = Mock(spec=Distribution)
    dist1.name = 'param1'
    dist1.region = 'region1'

    dist2 = Mock(spec=Distribution)
    dist2.name = 'param2'
    dist2.region = 'region1'

    dist3 = Mock(spec=Distribution)
    dist3.name = 'param3'
    dist3.region = 'region2'

    return [dist1, dist2, dist3]









# =============================================================================
# Entity Set Fixtures
# =============================================================================

@pytest.fixture
def sample_entity_sets():
    """Fixture providing sample entity sets."""
    return {
        'region1': EntitySet('region1', 'element', [1, 2, 3]),
        'region2': EntitySet('region2', 'element', [4]),
        'node_region': EntitySet('node_region', 'node', [0, 1, 2, 3]),
    }


@pytest.fixture
def empty_entity_sets():
    """Fixture providing empty entity sets."""
    return {}









# =============================================================================
# SG Assignment Fixtures
# =============================================================================

@pytest.fixture
def simple_sg_assignment():
    """Fixture providing a simple SG assignment."""
    sg_assign = Mock()
    sg_assign.name = 'sg_assign_1'
    sg_assign.region = 'region1'
    sg_assign.sg_model = 'test_sg_model'
    return sg_assign


@pytest.fixture
def simple_sg_assignments():
    """Fixture providing a list of simple SG assignments."""
    sg_assign_1 = Mock()
    sg_assign_1.name = 'sg_assign_1'
    sg_assign_1.region = 'region1'
    sg_assign_1.sg_model = 'test_sg_model'

    sg_assign_2 = Mock()
    sg_assign_2.name = 'sg_assign_2'
    sg_assign_2.region = 'region2'
    sg_assign_2.sg_model = 'test_sg_model'

    return [sg_assign_1, sg_assign_2]


@pytest.fixture
def mock_sg_assignment():
    """Fixture providing a mock SG assignment."""
    sg_assign = Mock()
    sg_assign.region = 'test_region'
    sg_assign.location = 'element'
    sg_assign.region_entities = [1, 2, 3]
    sg_assign.sg_model = 'test_sg_model'
    return sg_assign


@pytest.fixture
def sample_sg_assignments():
    """Fixture providing a list of sample SG assignments."""
    # Element-based assignment
    elem_assign = Mock()
    elem_assign.region = 'region1'
    elem_assign.location = 'element'
    elem_assign.region_entities = [1, 2, 3]
    elem_assign.sg_model = 'elem_sg_model'
    
    # Node-based assignment
    node_assign = Mock()
    node_assign.region = 'node_region'
    node_assign.location = 'node'
    node_assign.region_entities = [0, 1, 2]
    node_assign.sg_model = 'node_sg_model'
    
    return [elem_assign, node_assign]









# =============================================================================
# Parameter Value Fixtures
# =============================================================================

@pytest.fixture
def sample_node_param_values():
    """Fixture providing sample node parameter values."""
    return {
        'param1': {
            0: 1.0,
            1: 1.5,
            2: 2.0,
            3: 2.5,
        },
        'param2': {
            0: 10.0,
            1: 15.0,
            2: 20.0,
            3: 25.0,
        }
    }


# =============================================================================
# Utility Fixtures
# =============================================================================

@pytest.fixture
def small_tolerance():
    """Fixture providing a small numerical tolerance for comparisons."""
    return 1e-10


@pytest.fixture
def default_tolerance():
    """Fixture providing a default numerical tolerance for comparisons."""
    return 1e-6


# =============================================================================
# Parametrized Fixtures
# =============================================================================

@pytest.fixture(params=['triangle', 'quad', 'tetra', 'hexahedron'])
def cell_type(request):
    """Parametrized fixture for different cell types."""
    return request.param


@pytest.fixture(params=[1, 2, 4, 8])
def max_workers(request):
    """Parametrized fixture for different numbers of parallel workers."""
    return request.param


@pytest.fixture(params=['element', 'node', 'element_node'])
def sg_location(request):
    """Parametrized fixture for different SG assignment locations."""
    return request.param


# =============================================================================
# Complex Test Scenarios
# =============================================================================

@pytest.fixture
def complex_mesh_scenario():
    """Fixture providing a complex mesh scenario with multiple regions."""
    nodes = np.array([
        [0.0, 0.0], [1.0, 0.0], [2.0, 0.0], [3.0, 0.0],
        [0.0, 1.0], [1.0, 1.0], [2.0, 1.0], [3.0, 1.0],
        [0.0, 2.0], [1.0, 2.0], [2.0, 2.0], [3.0, 2.0],
    ])
    
    cells = [
        CellBlock('triangle', np.array([
            [0, 1, 4], [1, 5, 4], [1, 2, 5], [2, 6, 5],
            [4, 5, 8], [5, 9, 8], [5, 6, 9], [6, 10, 9],
        ])),
        CellBlock('quad', np.array([
            [2, 3, 7, 6], [6, 7, 11, 10],
        ]))
    ]
    
    etags = [
        np.array([1, 2, 3, 4, 5, 6, 7, 8]),  # triangle elements
        np.array([9, 10])                     # quad elements
    ]
    
    entity_sets = {
        'left_region': EntitySet('left_region', 'element', [1, 2, 5, 6]),
        'middle_region': EntitySet('middle_region', 'element', [3, 4, 7, 8]),
        'right_region': EntitySet('right_region', 'element', [9, 10]),
    }
    
    return {
        'nodes': nodes,
        'cells': cells,
        'etags': etags,
        'entity_sets': entity_sets
    }
